"use client"

import { useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import { MobileLayout, MobileContainer, MobileCard } from "@/components/ui/mobile-layout"
import { Eye, User, Loader2, Settings, BarChart3, Target, Calendar, TrendingUp, ArrowRight } from "lucide-react"
import { useUserContext } from "@/hooks/useUserContext"
import { useGameReviewData } from "@/hooks/useGameReviewData"
import { PerformanceOverviewGrid } from "@/components/game-review/layout/PerformanceOverviewGrid"
import { QuickActionsBar } from "@/components/game-review/layout/QuickActionsBar"
import { LoadingSkeletons } from "@/components/game-review/common/LoadingSkeletons"
import Link from "next/link"
import { DevelopmentNotice } from "@/components/development-notice"

export default function GameReviewPage() {
  // Global user context
  const { user, isLoading: isLoadingUser, error: userError } = useUserContext()

  // Game review data
  const {
    data: gameReviewData,
    isLoading: isLoadingStats,
    error: statsError,
    refetch
  } = useGameReviewData()

  // Get chess profiles from user data
  const chessProfiles = user?.chess_profiles || []

  // Check if we have data to display
  const hasData = gameReviewData && (gameReviewData.opponentMistakesTimeSeries || gameReviewData.myMistakesTimeSeries)

  return (
    <MobileLayout fullHeight>
      <div className="min-h-screen bg-gray-50">
        <Navigation />

        <MobileContainer padding="lg">
          {/* Development Notice */}
          <DevelopmentNotice className="mb-6" />

          <MobileCard padding="lg" className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
              <div>
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-orange-600 mb-2">Game Review & Analysis</h1>
                <p className="text-base sm:text-lg lg:text-xl text-gray-600">Track your tactical recognition and learn from missed opportunities</p>
              </div>
              <div className="flex items-center space-x-2 bg-orange-100 px-3 py-2 rounded-full self-start sm:self-auto">
                <Eye className="h-4 w-4 sm:h-5 sm:w-5 text-orange-600" />
                <span className="text-sm sm:text-base lg:text-lg font-semibold text-orange-800">
                  {isLoadingStats ? 'Loading...' : 'Analysis Ready'}
                </span>
              </div>
            </div>

            {/* Error Messages */}
            {userError && (
              <div className="mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-800 text-sm sm:text-base">User Error: {userError}</p>
              </div>
            )}

            {/* Loading State */}
            {isLoadingUser && (
              <div className="flex items-center justify-center py-8 sm:py-12">
                <Loader2 className="h-6 w-6 sm:h-8 sm:w-8 animate-spin text-orange-600" />
                <span className="ml-2 text-base sm:text-lg text-gray-600">Loading user data...</span>
              </div>
            )}

            {/* Chess Profile Connection Prompt */}
            {!isLoadingUser && chessProfiles.length === 0 && (
              <Card className="hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 mb-6">
                <CardHeader className="pb-3 sm:pb-4">
                  <CardTitle className="flex items-center space-x-2 text-blue-800 text-base sm:text-lg">
                    <User className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                    <span>Connect Chess Profiles</span>
                  </CardTitle>
                  <CardDescription className="text-blue-700 text-sm sm:text-base">
                    Connect your Lichess and Chess.com accounts to start analyzing your games and viewing statistics.
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-center py-6 sm:py-8">
                    <User className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-3 sm:mb-4 text-blue-300" />
                    <h3 className="text-base sm:text-lg font-semibold text-blue-900 mb-2">No Chess Profiles Connected</h3>
                    <p className="text-blue-700 mb-4 sm:mb-6 text-sm sm:text-base">
                      To view your game analysis and statistics, you need to connect at least one chess profile.
                    </p>
                    <Link href="/settings">
                      <Button className="bg-blue-600 hover:bg-blue-700 text-sm sm:text-base">
                        <Settings className="h-4 w-4 mr-2" />
                        Go to Settings
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}
          </MobileCard>

          {/* Quick Actions Bar - Moved to top */}
          {!isLoadingStats && chessProfiles.length > 0 && !statsError && (
            <QuickActionsBar hasData={hasData} data={gameReviewData} className="mb-6" />
          )}

          {/* Performance Overview Cards */}
          {!isLoadingStats && chessProfiles.length > 0 && !statsError && (
            <div className="mb-6">
              <PerformanceOverviewGrid
                data={gameReviewData}
                isLoading={isLoadingStats}
              />
            </div>
          )}

          {/* Loading State for Stats */}
          {isLoadingStats && chessProfiles.length > 0 && (
            <div className="mb-6">
              <LoadingSkeletons />
            </div>
          )}

          {/* Error State for Stats */}
          {statsError && !isLoadingStats && chessProfiles.length > 0 && (
            <MobileCard padding="lg" className="mb-6">
              <div className="text-center py-8 sm:py-12">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 sm:p-6">
                  <h3 className="text-base sm:text-lg font-semibold text-red-900 mb-2">Analysis Error</h3>
                  <p className="text-red-700 text-sm sm:text-base mb-4">{statsError}</p>
                  <Button
                    onClick={refetch}
                    className="bg-red-600 hover:bg-red-700 text-white text-sm sm:text-base"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </MobileCard>
          )}
        </MobileContainer>

        <Footer hideOnMobile={true} />
      </div>
    </MobileLayout>
  )
}
