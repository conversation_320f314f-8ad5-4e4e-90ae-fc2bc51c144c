"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { useAuthContext } from "@/components/auth/auth-provider"
import { APP_CONFIG, API_CONFIG, debugLog } from "@/lib/config"
import { hasValidAuth } from "@/lib/auth/tokens"

import { useChessUsernameValidation } from "@/hooks/useChessUsernameValidation"
import { ChessUsernameInput } from "@/components/ui/chess-username-input"
import { useChessProfiles } from "@/hooks/useChessProfiles"
import { useUserContext } from "@/hooks/useUserContext"
import { DevelopmentNotice } from "@/components/development-notice"

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    lichessUsername: "",
    chessComUsername: "",
  })
  const [localError, setLocalError] = useState<string | null>(null)
  const [validatingUsernames, setValidatingUsernames] = useState(false)
  const [creatingProfiles, setCreatingProfiles] = useState(false)
  const { register, isLoading, error, clearError, isAuthenticated } = useAuthContext()
  const router = useRouter()

  // Chess username validation
  const {
    chessComValidation,
    lichessValidation,
    validateChessComUsernameAsync,
    validateLichessUsernameAsync,
    clearValidation
  } = useChessUsernameValidation()

  // Chess profile creation
  const { createChessProfile } = useChessProfiles()
  const { updateChessProfiles } = useUserContext()

  // Debug: Log current configuration
  useEffect(() => {
    debugLog('Current API config:', {
      BASE_URL: API_CONFIG.BASE_URL,
      REGISTER_ENDPOINT: API_CONFIG.ENDPOINTS.REGISTER,
      FULL_REGISTER_URL: `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.REGISTER}`
    })
  }, [])

  // Check for tokens immediately
  const hasTokens = hasValidAuth()

  // Redirect immediately if authenticated (no useEffect delay)
  if (isAuthenticated) {
    router.push(APP_CONFIG.DEFAULT_LOGIN_REDIRECT)
    // Return loading state while redirect is happening
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <CardContent className="p-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Redirecting to dashboard...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show loading state if we have tokens (likely authenticated) or if auth is still loading
  if (isLoading || (hasTokens && !isAuthenticated)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <CardContent className="p-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Checking authentication...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Validate chess usernames and wait for any pending validations
  const validateUsernames = async (): Promise<boolean> => {
    setValidatingUsernames(true)
    setLocalError(null)

    try {
      // If usernames are provided, ensure they are validated
      if (formData.chessComUsername.trim()) {
        await validateChessComUsernameAsync(formData.chessComUsername)
        if (chessComValidation.isValid === false) {
          setLocalError(chessComValidation.error || "Chess.com username is invalid.")
          return false
        }
      }

      if (formData.lichessUsername.trim()) {
        await validateLichessUsernameAsync(formData.lichessUsername)
        if (lichessValidation.isValid === false) {
          setLocalError(lichessValidation.error || "Lichess username is invalid.")
          return false
        }
      }

      return true
    } catch (error) {
      setLocalError("Failed to validate chess usernames. Please try again.")
      return false
    } finally {
      setValidatingUsernames(false)
    }
  }

  // Create chess profiles after successful registration
  const createChessProfilesAfterRegistration = async (): Promise<void> => {
    if (!formData.lichessUsername.trim() && !formData.chessComUsername.trim()) {
      return // No chess usernames provided
    }

    setCreatingProfiles(true)

    try {
      const createdProfiles = []

      // Create Lichess profile if username provided
      if (formData.lichessUsername.trim()) {
        try {
          const lichessProfile = await createChessProfile('lichess.org', formData.lichessUsername.trim())
          createdProfiles.push(lichessProfile)
        } catch (error) {
          console.error('Failed to create Lichess profile:', error)
          // Don't fail the entire registration for profile creation errors
        }
      }

      // Create Chess.com profile if username provided
      if (formData.chessComUsername.trim()) {
        try {
          const chessComProfile = await createChessProfile('chess.com', formData.chessComUsername.trim())
          createdProfiles.push(chessComProfile)
        } catch (error) {
          console.error('Failed to create Chess.com profile:', error)
          // Don't fail the entire registration for profile creation errors
        }
      }

      // Update global user state with created profiles
      if (createdProfiles.length > 0) {
        updateChessProfiles(createdProfiles)
      }
    } catch (error) {
      console.error('Error creating chess profiles:', error)
      // Don't fail registration for profile creation errors
    } finally {
      setCreatingProfiles(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Register form submitted', formData)

    // Clear previous errors
    clearError()
    setLocalError(null)

    // Validation
    if (!formData.email || !formData.password) {
      setLocalError('Please fill in all required fields')
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setLocalError('Passwords do not match')
      return
    }

    if (formData.password.length < 6) {
      setLocalError('Password must be at least 6 characters long')
      return
    }

    // Validate chess usernames if provided
    if (formData.lichessUsername.trim() || formData.chessComUsername.trim()) {
      const isValid = await validateUsernames()
      if (!isValid) {
        return
      }
    }

    try {
      console.log('Calling register API with:', {
        email: formData.email,
        password: '***',
      })

      await register({
        email: formData.email,
        password: formData.password,
      })

      console.log('Registration successful')

      // Create chess profiles after successful registration
      await createChessProfilesAfterRegistration()

    } catch (error) {
      console.error('Registration failed:', error)
      // Error is handled by the auth context, but let's also log it
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Clear local errors when user starts typing
    if (localError) {
      setLocalError(null)
    }
    // Clear validation errors when user clears the field
    if (!value.trim() && (field === 'lichessUsername' || field === 'chessComUsername')) {
      clearValidation()
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-6">
        {/* Development Notice */}
        <DevelopmentNotice />

        <Card className="w-full">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold text-orange-600">Chessticize</CardTitle>
          <CardDescription>Create your account</CardDescription>
        </CardHeader>
        <CardContent>
          {(error || localError) && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{localError || error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleChange("email", e.target.value)}
                required
                className="mt-1"
                disabled={isLoading}
              />
            </div>

            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleChange("password", e.target.value)}
                required
                className="mt-1"
                disabled={isLoading}
              />
            </div>

            <div>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleChange("confirmPassword", e.target.value)}
                required
                className="mt-1"
                disabled={isLoading}
              />
            </div>

            <div className="space-y-4 pt-4 border-t border-gray-200">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Chess Profiles (Optional)</h3>
                <p className="text-sm text-gray-600">Connect your chess accounts to analyze your games</p>
              </div>

              <ChessUsernameInput
                id="lichessUsername"
                label="Lichess Username"
                placeholder="Your Lichess username (optional)"
                value={formData.lichessUsername}
                onChange={(value) => handleChange("lichessUsername", value)}
                onValidate={validateLichessUsernameAsync}
                validation={lichessValidation}
                disabled={isLoading || validatingUsernames || creatingProfiles}
              />

              <ChessUsernameInput
                id="chessComUsername"
                label="Chess.com Username"
                placeholder="Your Chess.com username (optional)"
                value={formData.chessComUsername}
                onChange={(value) => handleChange("chessComUsername", value)}
                onValidate={validateChessComUsernameAsync}
                validation={chessComValidation}
                disabled={isLoading || validatingUsernames || creatingProfiles}
              />
            </div>

            <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700" disabled={isLoading || validatingUsernames || creatingProfiles}>
              {isLoading ? "Creating account..." :
               validatingUsernames ? "Validating usernames..." :
               creatingProfiles ? "Setting up chess profiles..." :
               "Create account"}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <Link href="/login" className="text-orange-600 hover:text-orange-700 font-medium">
                Sign in
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
